<template>
  <view class="login-container">
    <view class="login-header">
      <image class="logo" src="/static/logo.png" mode="aspectFit"></image>
      <text class="app-name">西安市第五医院居家护理医护端</text>
    </view>

    <view class="login-content">
      <view class="user-info" v-if="userInfo">
        <image class="avatar" :src="userInfo.avatarUrl" mode="aspectFill"></image>
        <text class="nickname">{{ userInfo.nickname }}</text>
        <text class="welcome">欢迎回来！</text>
      </view>

      <view class="login-actions">
        <button v-if="!isLoggedIn" class="login-btn" @click="handleWeChatLogin" :loading="isLogging">
          {{ isLogging ? '登录中...' : '微信登录' }}
        </button>

        <button v-else class="logout-btn" @click="handleLogout">
          退出登录
        </button>
      </view>

      <view class="tips" v-if="!isLoggedIn">
        <text>点击登录即表示同意用户协议和隐私政策</text>
      </view>
    </view>
  </view>
</template>

<script setup>
  import { ref, onMounted } from 'vue';
  import { API_BASE_URL } from '@/utils/config.js';

  // 响应式数据
  const isLoggedIn = ref(false)
  const isLogging = ref(false)
  const userInfo = ref(null)
  const isAdmin = ref(false)

  // 检查登录状态
  const checkLoginStatus = () => {
    const token = uni.getStorageSync('token')
    const cachedUserInfo = uni.getStorageSync('userInfo')
    const cachedIsAdmin = uni.getStorageSync('isAdmin')

    if (token && cachedUserInfo) {
      isLoggedIn.value = true
      userInfo.value = cachedUserInfo
      isAdmin.value = cachedIsAdmin

      // Validate token before navigating
      validateToken(token).then(isValid => {
        if (isValid) {
          setTimeout(() => {
            uni.switchTab({
              url: '/pages/home/<USER>',
            });
          }, 1000);
        } else {
          // Token is invalid, force user to log in again
          uni.removeStorageSync('token')
          uni.removeStorageSync('userInfo')
          uni.removeStorageSync('isAdmin')
          
          // 重置状态
          isLoggedIn.value = false
          userInfo.value = null
        }
      });
    }
  }

  // 微信登录
  const handleWeChatLogin = async () => {
    try {
      isLogging.value = true

      const userProfile = await getUserProfile()

      const loginRes = await new Promise((resolve, reject) => {
        uni.login({
          provider: 'weixin',
          success: resolve,
          fail: reject
        })
      })
      
      console.log(loginRes)

      const loginResult = await loginToServer(loginRes.code)

      if (loginResult.token) {
        const dbUser = loginResult.user || {};

        const storedUserInfo = {
          nickname: dbUser.nickname || userProfile.nickName,
          avatarUrl: dbUser.avatar || userProfile.avatarUrl,
        };

        uni.setStorageSync('token', loginResult.token);
        uni.setStorageSync('userInfo', storedUserInfo);
        uni.setStorageSync('isAdmin', loginResult.is_admin);

        userInfo.value = storedUserInfo;
        isLoggedIn.value = true
        isAdmin.value = loginResult.is_admin

        uni.showToast({
          title: '登录成功',
          icon: 'success'
        })
        
        setTimeout(() => {
          uni.switchTab({
            url: '/pages/home/<USER>',
          })
        }, 1000)
      } else {
        throw new Error('登录失败，未获取到token')
      }

    } catch (error) {
      console.error('登录失败:', error)
      uni.showToast({
        title: error.message || '登录失败',
        icon: 'none'
      })
    } finally {
      isLogging.value = false
    }
  }

  // 获取用户信息
  const getUserProfile = () => {
    return new Promise((resolve, reject) => {
      uni.getUserProfile({
        desc: '用于完善用户资料',
        success: (res) => {
          resolve(res.userInfo)
        },
        fail: (error) => {
          console.error('获取用户信息失败:', error)
          reject(new Error('获取用户信息失败'))
        }
      })
    })
  }

  // 发送登录信息到服务器
  const loginToServer = async (code) => {
    try {
      const response = await new Promise((resolve, reject) => {
        uni.request({
          url: `${API_BASE_URL}/auth/login`,
          method: 'POST',
          data: {
            app_key: 'doctor',
            code: code
          },
          header: {
            'Content-Type': 'application/json'
          },
          success: (res) => {
            if (res.statusCode === 200) {
              resolve(res.data)
            } else {
              reject(new Error(`请求失败: ${res.statusCode}`))
            }
          },
          fail: reject
        })
      })

      return response
    } catch (error) {
      console.error('服务器登录失败:', error)
      throw error
    }
  }

  // 退出登录
  const handleLogout = () => {
    uni.showModal({
      title: '提示',
      content: '确定要退出登录吗？',
      success: (res) => {
        if (res.confirm) {
          // 清除本地存储
          uni.removeStorageSync('token')
          uni.removeStorageSync('userInfo')
          uni.removeStorageSync('isAdmin')

          // 重置状态
          isLoggedIn.value = false
          userInfo.value = null

          uni.showToast({
            title: '已退出登录',
            icon: 'success'
          })
        }
      }
    })
  }

  const validateToken = (token) => {
    return new Promise((resolve) => {
      uni.request({
        url: `${API_BASE_URL}/auth/me`,
        method: 'GET',
        header: {
          'Authorization': `Bearer ${token}`
        },
        success: (res) => {
          resolve(res.statusCode === 200);
        },
        fail: () => {
          resolve(false);
        }
      });
    });
  };



  onMounted(() => {
    checkLoginStatus()
  })
</script>

<style lang="scss" scoped>
  .login-container {
    min-height: 100vh;
    background: linear-gradient(135deg, #667eea 0%, #b29f69 100%);
    display: flex;
    flex-direction: column;
    padding: 0 60rpx;
  }

  .login-header {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding-top: 200rpx;

    .logo {
      width: 160rpx;
      height: 160rpx;
      border-radius: 20rpx;
      margin-bottom: 40rpx;
    }

    .app-name {
      font-size: 40rpx;
      font-weight: bold;
      color: #ffffff;
      text-align: center;
    }
  }

  .login-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    padding-top: 100rpx;
    padding-bottom: 100rpx;
  }

  .user-info {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: 80rpx;

    .avatar {
      width: 120rpx;
      height: 120rpx;
      border-radius: 60rpx;
      margin-bottom: 20rpx;
    }

    .nickname {
      font-size: 36rpx;
      font-weight: bold;
      color: #ffffff;
      margin-bottom: 10rpx;
    }

    .welcome {
      font-size: 28rpx;
      color: rgba(255, 255, 255, 0.8);
    }
  }

  .login-actions {
    margin-bottom: 60rpx;

    .login-btn,
    .logout-btn {
      width: 100%;
      height: 96rpx;
      background: #ffffff;
      color: #667eea;
      border: none;
      border-radius: 48rpx;
      font-size: 36rpx;
      font-weight: bold;
      display: flex;
      align-items: center;
      justify-content: center;
      box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.1);
    }

    .login-btn:active,
    .logout-btn:active {
      transform: scale(0.98);
      opacity: 0.9;
    }

    .logout-btn {
      background: rgba(255, 255, 255, 0.2);
      color: #ffffff;
      border: 2rpx solid rgba(255, 255, 255, 0.3);
    }
  }

  .tips {
    text-align: center;

    text {
      font-size: 24rpx;
      color: rgba(255, 255, 255, 0.7);
      line-height: 1.5;
    }
  }
</style>
