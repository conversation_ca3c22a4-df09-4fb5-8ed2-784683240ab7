from typing import List, Optional
from fastapi import APIRouter, HTTPException, Query

from src.nurses.models import Nurse, Nurse<PERSON><PERSON>, NurseRead, NurseUpdate, NurseWithUser
from src.nurses.service import NurseService

from src.auth.auth import CurrentUser
from src.db.session import DBSession

nurse_router = APIRouter(prefix="/nurses", tags=["nurses"])


@nurse_router.post("/", response_model=NurseRead)
async def create_nurse(
    nurse_data: NurseCreate,
    session: DBSession,
    current_user: CurrentUser
):
    """Create a new nurse (Admin only)"""
    nurse = await NurseService(session).create_nurse_with_user(nurse_data)
    return nurse


@nurse_router.get("/", response_model=List[NurseRead])
async def get_nurses(
    session: DBSession,
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=100),
    department: Optional[str] = Query(None),
    is_active: Optional[bool] = Query(None)
):
    """Get list of nurses with optional filtering"""
    nurses = await NurseService(session).get_nurses(
        skip=skip, 
        limit=limit,
        department=department,
        is_active=is_active
    )
    return nurses


@nurse_router.get("/available", response_model=List[NurseRead])
async def get_available_nurses(
    session: DBSession,
    department: Optional[str] = Query(None)
):
    """Get available nurses for assignment"""
    nurses = await NurseService(session).get_available_nurses(
        department=department
    )
    return nurses


@nurse_router.get("/search", response_model=List[NurseRead])
async def search_nurses(
    session: DBSession,
    q: str = Query(..., min_length=1)
):
    """Search nurses by name or employee ID"""
    nurses = await NurseService(session).search_nurses(q)
    return nurses


@nurse_router.get("/department/{department}", response_model=List[NurseRead])
async def get_nurses_by_department(
    department: str,
    session: DBSession
):
    """Get all active nurses in a specific department"""
    nurses = await NurseService(session).get_nurses_by_department(department)
    return nurses


@nurse_router.get("/{nurse_id}", response_model=NurseRead)
async def get_nurse(
    nurse_id: int,
    session: DBSession
):
    """Get a specific nurse by ID"""
    nurse = await NurseService(session).get_nurse(nurse_id)
    if not nurse:
        raise HTTPException(status_code=404, detail="Nurse not found")
    return nurse


@nurse_router.patch("/{nurse_id}", response_model=NurseRead)
async def update_nurse(
    nurse_id: int,
    nurse_update: NurseUpdate,
    session: DBSession
):
    """Update a nurse (Admin only)"""
    nurse = await NurseService(session).update_nurse(nurse_id, nurse_update)
    if not nurse:
        raise HTTPException(status_code=404, detail="Nurse not found")
    return nurse


@nurse_router.delete("/{nurse_id}")
async def delete_nurse(
    nurse_id: int,
    session: DBSession
):
    """Soft delete a nurse (Admin only)"""
    success = await NurseService(session).delete_nurse(nurse_id)
    if not success:
        raise HTTPException(status_code=404, detail="Nurse not found")
    return {"message": "Nurse deleted successfully"}
