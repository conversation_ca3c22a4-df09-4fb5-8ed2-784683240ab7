from datetime import datetime, timedelta
from typing import Annotated, Optional
from fastapi import Depends, HTTPException
from fastapi.security import <PERSON>A<PERSON>2<PERSON><PERSON>wordBearer
from sqlmodel import select

from jose import jwt, JWTError

from src.db.session import DBSession
from src.users.models import User
from src.utils.config import settings
from src.auth.password import verify_password


oauth2_scheme = OAuth2PasswordBearer(tokenUrl="auth/login")

def create_access_token(uid: str, openid: Optional[str] = None, expires_delta: Optional[timedelta] = None):
    if expires_delta:
        expire = datetime.now() + expires_delta
    else:
        expire = datetime.now() + timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
    
    payload = {"uid": uid, "exp": expire}
    if openid:
        payload["openid"] = openid
        
    return jwt.encode(payload, settings.JWT_SECRET, algorithm=settings.JWT_ALGORITHM)


async def get_current_user(session: DBSession, token: str = Depends(oauth2_scheme)) -> User:
    credential_exception = HTTPException(
        status_code=401,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )
    try:
        payload = jwt.decode(token, settings.JWT_SECRET, algorithms=[settings.JWT_ALGORITHM])
        uid: str = payload.get("uid")
        if uid is None:
            raise credential_exception
        
        user_id = int(uid)
    except JWTError:
        raise credential_exception
    
    user = await session.get(User, user_id)
    if user is None:
        raise credential_exception
    
    return user

async def get_current_user_flexible(session: DBSession, token: str = Depends(oauth2_scheme)) -> User:
    """Get current user without requiring active status - for flexible auth"""
    credential_exception = HTTPException(
        status_code=401,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )
    try:
        payload = jwt.decode(token, settings.JWT_SECRET, algorithms=[settings.JWT_ALGORITHM])
        uid: str = payload.get("uid")
        if uid is None:
            raise credential_exception
    except JWTError:
        raise credential_exception
    
    user = await session.get(User, uid)
    if user is None:
        raise credential_exception
    
    return user

async def get_current_user_any_auth(current_user: User = Depends(get_current_user_flexible)) -> User:
    """Allow both WeChat users and admin users"""
    if not current_user.is_active:
        raise HTTPException(status_code=400, detail="Inactive user")
    return current_user

# New type annotations for flexible auth
FlexibleUser = Annotated[User, Depends(get_current_user_any_auth)]


async def authenticate_user(session: DBSession, username: str, password: str) -> Optional[User]:
    result = await session.exec(select(User).where(User.username == username))
    user = result.one_or_none()
    if not user:
        return None
    if not user.hashed_password:
        return None
    if not verify_password(password, user.hashed_password):
        return None
    return user

async def get_current_active_user(current_user: User = Depends(get_current_user)) -> User:
    if not current_user.is_active:
        raise HTTPException(status_code=400, detail="Inactive user")
    return current_user


async def get_current_active_admin_user(current_user: User = Depends(get_current_user)):
    if not current_user.is_active:
        raise HTTPException(status_code=400, detail="Inactive user")
    if not current_user.is_admin:
        raise HTTPException(status_code=403, detail="The user doesn't have enough privileges")
    return current_user

CurrentUser = Annotated[User, Depends(get_current_active_user)]
AdminUser = Annotated[User, Depends(get_current_active_admin_user)]
